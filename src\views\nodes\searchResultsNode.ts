import type { TreeItem } from 'vscode';
import { ThemeIcon } from 'vscode';
import { md5 } from '@env/crypto';
import type { SearchQuery } from '../../constants.search';
import { executeGitCommand } from '../../git/actions';
import type { GitLog } from '../../git/models/log';
import type { CommitsQueryResults } from '../../git/queryResults';
import { getSearchQueryComparisonKey, getStoredSearchQuery } from '../../git/search';
import { pluralize } from '../../system/string';
import type { SearchAndCompareView } from '../searchAndCompareView';
import type { ViewNode } from './abstract/viewNode';
import { ContextValues, getViewNodeId } from './abstract/viewNode';
import { ResultsCommitsNode } from './resultsCommitsNode';

interface SearchQueryResults {
	readonly label: string;
	readonly log: GitLog | undefined;
	readonly hasMore: boolean;
	more?(limit: number | undefined): Promise<void>;
}

export class SearchResultsNode extends ResultsCommitsNode<SearchAndCompareView> {
	private _search: SearchQuery;
	private _labels: {
		label: string;
		queryLabel: string | { label: string; resultsType?: { singular: string; plural: string } };
		resultsType?: { singular: string; plural: string };
	};
	private _storedAt: number;

	constructor(
		view: SearchAndCompareView,
		parent: ViewNode,
		repoPath: string,
		search: SearchQuery,
		labels: {
			label: string;
			queryLabel: string | { label: string; resultsType?: { singular: string; plural: string } };
			resultsType?: { singular: string; plural: string };
		},
		searchQueryOrLog?:
			| ((limit: number | undefined) => Promise<CommitsQueryResults>)
			| Promise<GitLog | undefined>
			| GitLog
			| undefined,
		storedAt: number = 0,
	) {
		// Store the search-specific properties first
		const searchInstance = {
			_search: search,
			_labels: labels,
			view: view,
			repoPath: repoPath,
		};

		// Prepare the query function for the parent constructor
		let queryFunction: (limit: number | undefined) => Promise<CommitsQueryResults>;
		let deferred = false;

		if (searchQueryOrLog == null) {
			deferred = true;
			queryFunction = (limit: number | undefined) => {
				return SearchResultsNode.prototype.getSearchQuery.call(searchInstance, { label: labels.queryLabel })(
					limit,
				);
			};
		} else if (typeof searchQueryOrLog !== 'function') {
			queryFunction = (limit: number | undefined) => {
				return SearchResultsNode.prototype.getSearchQuery.call(
					searchInstance,
					{ label: labels.queryLabel },
					searchQueryOrLog,
				)(limit);
			};
		} else {
			queryFunction = searchQueryOrLog;
		}

		super(view, parent, repoPath, labels.label, { query: queryFunction, deferred: deferred }, { expand: false });

		this._search = search;
		this._labels = labels;
		this._storedAt = storedAt;

		// Override the type and context for SearchResultsNode
		(this as any).type = 'search-results';
		this.updateContext({ searchId: getSearchQueryComparisonKey(this._search) });
		this._uniqueId = getViewNodeId('search-results', this.context);

		// If this is a new search, save it
		if (this._storedAt === 0) {
			this._storedAt = Date.now();
			void this.store(true).catch();
		}
	}

	override get id(): string {
		return this._uniqueId;
	}

	override toClipboard(): string {
		return this.search.query;
	}

	get order(): number {
		return this._storedAt;
	}

	get search(): SearchQuery {
		return this._search;
	}

	dismiss(): void {
		void this.remove(true);
	}

	override async getTreeItem(): Promise<TreeItem> {
		const item = await super.getTreeItem();
		item.id = this.id;
		item.contextValue = ContextValues.SearchResults;
		if (this.view.container.git.repositoryCount > 1) {
			const repo = this.view.container.git.getRepository(this.repoPath);
			item.description = repo?.name ?? this.repoPath;
		}
		item.iconPath = new ThemeIcon('search');

		return item;
	}

	async edit(search?: {
		pattern: SearchQuery;
		labels: {
			label: string;
			queryLabel:
				| string
				| {
						label: string;
						resultsType?: { singular: string; plural: string };
				  };
			resultsType?: { singular: string; plural: string };
		};
		log: Promise<GitLog | undefined> | GitLog | undefined;
	}): Promise<void> {
		if (search == null) {
			await executeGitCommand({
				command: 'search',
				prefillOnly: true,
				state: {
					repo: this.repoPath,
					...this.search,
					showResultsInSideBar: this,
				},
			});

			return;
		}

		// Save the current id so we can update it later
		const currentId = this.getStorageId();

		this._search = search.pattern;
		this._labels = search.labels;

		// Remove the existing stored item and save a new one
		await this.replace(currentId, true);

		void this.triggerChange(false);
		queueMicrotask(() => this.view.reveal(this, { expand: true, focus: true, select: true }));
	}

	private getSearchLabel(
		label:
			| string
			| {
					label: string;
					resultsType?: { singular: string; plural: string };
			  },
		log: GitLog | undefined,
	): string {
		if (typeof label === 'string') return label;

		const count = log?.count ?? 0;

		const resultsType =
			label.resultsType === undefined
				? { singular: 'search result', plural: 'search results' }
				: label.resultsType;

		return `${pluralize(resultsType.singular, count, {
			format: c => (log?.hasMore ? `${c}+` : String(c)),
			plural: resultsType.plural,
			zero: 'No',
		})} ${label.label}`;
	}

	private getSearchQuery(
		options: {
			label:
				| string
				| {
						label: string;
						resultsType?: { singular: string; plural: string };
				  };
		},
		log?: Promise<GitLog | undefined> | GitLog,
	): (limit: number | undefined) => Promise<SearchQueryResults> {
		let useCacheOnce = true;

		return async (limit: number | undefined) => {
			log = await (log ??
				this.view.container.git.getRepositoryService(this.repoPath).commits.searchCommits(this.search));

			if (!useCacheOnce && log?.query != null) {
				log = await log.query(limit);
			}
			useCacheOnce = false;

			const results: Mutable<SearchQueryResults> = {
				label: this.getSearchLabel(options.label, log),
				log: log,
				hasMore: log?.hasMore ?? false,
			};
			if (results.hasMore) {
				results.more = async (limit: number | undefined) => {
					results.log = (await results.log?.more?.(limit)) ?? results.log;

					results.label = this.getSearchLabel(options.label, results.log);
					results.hasMore = results.log?.hasMore ?? true;
				};
			}

			return results;
		};
	}

	private getStorageId() {
		return md5(`${this.repoPath}|${getSearchQueryComparisonKey(this.search)}`, 'base64');
	}

	private remove(silent: boolean = false) {
		return this.view.updateStorage(this.getStorageId(), undefined, silent);
	}

	private async replace(id: string, silent: boolean = false) {
		await this.view.updateStorage(id, undefined, silent);
		return this.store(silent);
	}

	private store(silent: boolean = false) {
		return this.view.updateStorage(
			this.getStorageId(),
			{
				type: 'search',
				timestamp: this._storedAt,
				path: this.repoPath,
				labels: this._labels,
				search: getStoredSearchQuery(this.search),
			},
			silent,
		);
	}
}
